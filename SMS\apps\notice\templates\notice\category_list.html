{% extends "notice/base.html" %}
{% load static %}

{% block title %}Notice Categories{% endblock %}
{% block title-icon %}fas fa-tags{% endblock %}
{% block subtitle %}Manage notice categories{% endblock %}

{% block page-actions %}
<a href="{% url 'notice:category_create' %}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>Add Category
</a>
<a href="{% url 'notice:list' %}" class="btn btn-outline-secondary ms-2">
    <i class="fas fa-arrow-left me-1"></i>Back to Notices
</a>
{% endblock %}

{% block content %}
<!-- Categories Grid -->
<div class="row">
    {% for category in categories %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center" style="background-color: {{ category.color }}; color: white;">
                <h6 class="mb-0">{{ category.name }}</h6>
                <div>
                    {% if category.is_active %}
                        <span class="badge bg-light text-dark">Active</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <p class="card-text">{{ category.description|default:"No description provided." }}</p>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>Created: {{ category.created_at|date:"M d, Y" }}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <div>
                        <span class="badge" style="background-color: {{ category.color }}; color: white;">
                            {{ category.color }}
                        </span>
                    </div>
                    <div>
                        <a href="{% url 'notice:category_create' %}" class="btn btn-sm btn-outline-primary me-1" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-sm btn-outline-danger" title="Delete" onclick="deleteCategory('{{ category.id }}', '{{ category.name }}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Categories Found</h4>
            <p class="text-muted">Create your first notice category to organize notices better.</p>
            <a href="{% url 'notice:category_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Create First Category
            </a>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ categories|length }}</h4>
                        <p class="mb-0">Total Categories</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ categories|length }}</h4>
                        <p class="mb-0">Active Categories</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>0</h4>
                        <p class="mb-0">Inactive Categories</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>Admin</h4>
                        <p class="mb-0">Your Role</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block morejs %}
<script>
function deleteCategory(categoryId, categoryName) {
    if (confirm('Are you sure you want to delete the category "' + categoryName + '"? This action cannot be undone.')) {
        // You can implement AJAX delete here or redirect to a delete confirmation page
        alert('Delete functionality would be implemented here');
    }
}

// Add some hover effects
$(document).ready(function() {
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg').css('transform', 'translateY(-2px)');
        },
        function() {
            $(this).removeClass('shadow-lg').css('transform', 'translateY(0)');
        }
    );
});
</script>
{% endblock %}
