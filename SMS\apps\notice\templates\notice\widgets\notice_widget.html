{% load static %}

<!-- Notice Widget for Dashboard -->
<div class="card notice-widget">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
            <i class="fas fa-bullhorn me-2"></i>Recent Notices
        </h6>
        <div>
            {% if unread_count > 0 %}
            <span class="badge bg-warning">{{ unread_count }} unread</span>
            {% endif %}
            <a href="{% url 'notice:list' %}" class="btn btn-sm btn-outline-primary ms-2">
                View All
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        {% if recent_notices %}
        <div class="list-group list-group-flush">
            {% for notice in recent_notices %}
            <div class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">
                        <a href="{% url 'notice:detail' notice.pk %}" class="text-decoration-none">
                            {{ notice.title|truncatechars:50 }}
                            {% if notice.is_important %}
                                <i class="fas fa-star text-warning ms-1"></i>
                            {% endif %}
                        </a>
                    </h6>
                    <small class="text-muted">{{ notice.created_at|timesince }} ago</small>
                </div>
                <p class="mb-1 text-muted small">{{ notice.content|truncatewords:15 }}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>{{ notice.created_by.get_full_name|default:notice.created_by.username }}
                    </small>
                    <span class="badge bg-{{ notice.priority == 'urgent' and 'danger' or notice.priority == 'high' and 'warning' or notice.priority == 'medium' and 'info' or 'success' }} badge-sm">
                        {{ notice.get_priority_display }}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-bullhorn fa-2x text-muted mb-2"></i>
            <p class="text-muted mb-0">No recent notices</p>
        </div>
        {% endif %}
    </div>
    {% if user_role == 'admin' or user_role == 'teacher' %}
    <div class="card-footer text-center">
        <a href="{% url 'notice:quick_create' %}" class="btn btn-sm btn-warning me-2">
            <i class="fas fa-bolt me-1"></i>Quick Notice
        </a>
        <a href="{% url 'notice:create' %}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i>Create Notice
        </a>
    </div>
    {% endif %}
</div>

<style>
.notice-widget .badge-sm {
    font-size: 0.7em;
    padding: 2px 6px;
}

.notice-widget .list-group-item {
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.notice-widget .list-group-item:hover {
    border-left-color: #007bff;
    background-color: #f8f9fa;
}
</style>
