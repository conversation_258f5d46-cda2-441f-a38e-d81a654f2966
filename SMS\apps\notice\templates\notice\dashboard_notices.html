{% extends "notice/base.html" %}
{% load static %}

{% block title %}Notice Dashboard{% endblock %}
{% block title-icon %}fas fa-bullhorn{% endblock %}
{% block subtitle %}Quick overview of recent notices{% endblock %}

{% block page-actions %}
{% if user_role == 'admin' or user_role == 'teacher' %}
<div>
    <a href="{% url 'notice:list' %}" class="btn btn-primary">
        <i class="fas fa-list me-1"></i>View All Notices
    </a>
    <a href="{% url 'notice:quick_create' %}" class="btn btn-warning ms-2">
        <i class="fas fa-bolt me-1"></i>Quick Notice
    </a>
</div>
{% endif %}
{% endblock %}

{% block content %}
<!-- Statistics -->
<div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ recent_notices|length }}</h4>
                            <p class="mb-0">Recent Notices</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullhorn fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ unread_count }}</h4>
                            <p class="mb-0">Unread Notices</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Notices -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Notices</h5>
                </div>
                <div class="card-body">
                    {% if recent_notices %}
                    <div class="list-group list-group-flush">
                        {% for notice in recent_notices %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">
                                    <a href="{% url 'notice:detail' notice.pk %}" class="text-decoration-none">
                                        {{ notice.title }}
                                        {% if notice.is_important %}
                                            <i class="fas fa-star text-warning ms-1"></i>
                                        {% endif %}
                                    </a>
                                </div>
                                <p class="mb-1 text-muted">{{ notice.content|truncatewords:20 }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ notice.created_by.get_full_name|default:notice.created_by.username }}
                                    <i class="fas fa-calendar ms-2 me-1"></i>{{ notice.created_at|date:"M d, Y" }}
                                    <i class="fas fa-clock ms-2 me-1"></i>{{ notice.created_at|time:"H:i" }}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{{ notice.priority == 'urgent' and 'danger' or notice.priority == 'high' and 'warning' or notice.priority == 'medium' and 'info' or 'success' }}">
                                    {{ notice.get_priority_display }}
                                </span>
                                <div class="mt-1">
                                    <small class="text-muted">{{ notice.get_recipient_type_display }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Recent Notices</h5>
                        <p class="text-muted">There are no recent notices to display.</p>
                        {% if user_role == 'admin' or user_role == 'teacher' %}
                        <a href="{% url 'notice:create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create First Notice
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                {% if recent_notices %}
                <div class="card-footer text-center">
                    <a href="{% url 'notice:list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-1"></i>View All Notices
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
