{% extends "corecode/base.html" %}
{% load static %}

{% block title %}Notice System - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .notice-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    
    .notice-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .priority-low { border-left-color: #28a745; }
    .priority-medium { border-left-color: #ffc107; }
    .priority-high { border-left-color: #fd7e14; }
    .priority-urgent { border-left-color: #dc3545; }
    
    .notice-status {
        font-size: 0.8em;
        padding: 2px 8px;
        border-radius: 12px;
        color: white;
    }
    
    .status-draft { background-color: #6c757d; }
    .status-published { background-color: #28a745; }
    .status-archived { background-color: #dc3545; }
    
    .unread-notice {
        background-color: #f8f9fa;
        border-left-color: #007bff !important;
        border-left-width: 6px !important;
    }
    
    .important-notice {
        background: linear-gradient(45deg, #fff3cd, #ffffff);
        border: 2px solid #ffc107;
    }
    
    .notice-meta {
        font-size: 0.9em;
        color: #6c757d;
    }
    
    .attachment-icon {
        color: #007bff;
    }
    
    .category-badge {
        font-size: 0.75em;
        padding: 3px 8px;
        border-radius: 10px;
        color: white;
    }
    
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .quick-actions {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }
    
    .btn-floating {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }
    
    .notice-content {
        max-height: 100px;
        overflow: hidden;
        position: relative;
    }
    
    .notice-content.expanded {
        max-height: none;
    }
    
    .read-more {
        color: #007bff;
        cursor: pointer;
        font-size: 0.9em;
    }
    
    @media (max-width: 768px) {
        .filter-section {
            padding: 10px;
        }
        
        .quick-actions {
            bottom: 10px;
            right: 10px;
        }
        
        .btn-floating {
            width: 48px;
            height: 48px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // Read more functionality
    $(document).ready(function() {
        $('.read-more').click(function() {
            var content = $(this).siblings('.notice-content');
            if (content.hasClass('expanded')) {
                content.removeClass('expanded');
                $(this).text('Read more...');
            } else {
                content.addClass('expanded');
                $(this).text('Read less');
            }
        });
        
        // Mark notice as read when clicked
        $('.notice-card').click(function() {
            var noticeId = $(this).data('notice-id');
            if (noticeId) {
                $.post('{% url "notice:mark_read" 0 %}'.replace('0', noticeId), {
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                });
            }
        });
        
        // Filter form auto-submit
        $('.filter-form select, .filter-form input[type="checkbox"]').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
